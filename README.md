# YEZHome_Deploy
CI/CD configuration for Y<PERSON>ZH<PERSON> with automatic SSL certificates and Nginx reverse proxy.

## 📖 Documentation

**[📚 Comprehensive Deployment Guide](COMPREHENSIVE_DEPLOYMENT_GUIDE.md)** - Complete guide covering:
- ✅ **Automatic SSL certificates** with Let's Encrypt
- ✅ **Nginx reverse proxy** with security headers
- ✅ **Multi-environment deployment** (staging/production)
- ✅ **GitHub Actions automation**
- ✅ **Manual deployment options**
- ✅ **Monitoring and troubleshooting**

## 🚀 Quick Start

1. **Configure GitHub Secrets** (see guide for complete list)
2. **Push to main branch** → Auto-deploy to staging
3. **Manual deploy to production** via GitHub Actions

## 🔗 Quick Links

- **[Deployment Script](deploy.sh)** - One-command deployment
- **[SSL Guide](ssl-deployment-guide.md)** - SSL-specific documentation
- **[GitHub Actions](.github/workflows/)** - Automated deployment workflows