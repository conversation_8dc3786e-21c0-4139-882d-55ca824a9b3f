#!/bin/sh
# SSL Certificate Initialization Script (Optimized Version)
set -e

echo "🔐 Starting SSL certificate initialization..."

# --- Biến môi trường ---
# CERTBOT_STAGING=true để dùng môi trường thử nghiệm của Let's Encrypt
STAGING=${CERTBOT_STAGING:-false}
EMAIL=${CERTBOT_EMAIL}
DOMAINS=${DOMAINS} # Ví dụ: "api.yezhome.vn,adapi.yezhome.vn"

# --- Kiểm tra biến môi trường ---
if [ -z "$EMAIL" ] || [ -z "$DOMAINS" ]; then
    echo "❌ CERTBOT_EMAIL and DOMAINS environment variables are required"
    exit 1
fi

# Lấy domain đầu tiên làm tên chính cho chứng chỉ
FIRST_DOMAIN=$(echo "$DOMAINS" | cut -d',' -f1)
CERT_PATH="/etc/letsencrypt/live/$FIRST_DOMAIN/fullchain.pem"

# --- <PERSON><PERSON><PERSON> chứng chỉ đã tồn tại, không làm gì cả ---
if [ -f "$CERT_PATH" ]; then
    echo "✅ Certificate already exists for $FIRST_DOMAIN. Skipping."
    # Vẫn cần cài đặt cron job cho các lần chạy sau
else
    echo "ℹ️ Certificate not found. Starting generation process..."

    # --- Bước 1: Tạo chứng chỉ tự ký (fallback) để Nginx có thể khởi động ---
    echo "📝 Generating fallback certificate for $FIRST_DOMAIN to allow Nginx to start..."
    # Tạo thư mục cho tất cả các domain để Nginx không báo lỗi
    for domain in $(echo "$DOMAINS" | tr ',' ' '); do
        mkdir -p "/etc/letsencrypt/live/$domain"
    done
    
    openssl req -x509 -nodes -newkey rsa:2048 -days 365 \
        -keyout "/etc/letsencrypt/live/$FIRST_DOMAIN/privkey.pem" \
        -out "/etc/letsencrypt/live/$FIRST_DOMAIN/fullchain.pem" \
        -subj "/CN=$FIRST_DOMAIN"
    
    # Symlink chứng chỉ fallback cho các domain khác
    for domain in $(echo "$DOMAINS" | tr ',' ' '); do
        if [ "$domain" != "$FIRST_DOMAIN" ]; then
            ln -sf "/etc/letsencrypt/live/$FIRST_DOMAIN/privkey.pem" "/etc/letsencrypt/live/$domain/privkey.pem"
            ln -sf "/etc/letsencrypt/live/$FIRST_DOMAIN/fullchain.pem" "/etc/letsencrypt/live/$domain/fullchain.pem"
        fi
    done
    echo "✅ Fallback certificate created."

    # --- Bước 2: Yêu cầu chứng chỉ thật trong nền ---
    # Chạy tiến trình này trong nền để không chặn việc khởi động của Nginx
    (
        echo "⏳ Waiting for Nginx to be ready for ACME challenge..."
        # Vòng lặp chờ Nginx sẵn sàng trên port 80
        until nc -z localhost 80; do
            sleep 5
        done
        echo "✅ Nginx is ready."

        echo "🌐 Requesting a single Let's Encrypt certificate for all domains: $DOMAINS"

        # Xóa fallback certs trước khi lấy cert thật
        rm -rf /etc/letsencrypt/live/*
        rm -rf /etc/letsencrypt/archive/*
        rm -rf /etc/letsencrypt/renewal/*

        local staging_flag=""
        if [ "$STAGING" = "true" ]; then
            staging_flag="--staging"
            echo "⚠️ Using Let's Encrypt staging environment"
        fi

        # Chuyển đổi dấu phẩy thành các cờ -d cho certbot
        CERTBOT_DOMAINS=$(echo "$DOMAINS" | sed 's/,/ -d /g')

        certbot certonly --webroot --webroot-path=/var/www/certbot \
            --email "$EMAIL" \
            --agree-tos \
            --no-eff-email \
            $staging_flag \
            -d $CERTBOT_DOMAINS
        
        echo "🔄 Reloading Nginx to apply the new Let's Encrypt certificate..."
        nginx -s reload
    ) &
fi

# --- Bước 3: Cài đặt Cron Job để tự động gia hạn ---
echo "⏰ Setting up certificate renewal cron job..."
echo "0 12 * * * certbot renew --quiet && nginx -s reload" > /etc/crontabs/root

echo "🎉 SSL initialization process is complete."