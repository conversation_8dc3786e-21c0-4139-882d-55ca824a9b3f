# YEZHome SSL Deployment Guide

This guide explains how to deploy YEZHome with automatic SSL certificates using Nginx and Let's Encrypt.

## Domain Mapping

### Production
- **Website Frontend**: yezhome.vn
- **API**: api.yezhome.vn
- **Internal API**: adapi.yezhome.vn

### Staging
- **Website Frontend**: test.yezhome.vn
- **API**: api-test.yezhome.vn
- **Internal API**: adapi-test.yezhome.vn

## Prerequisites

1. **Domain Configuration**: Ensure your domains are pointing to your server's IP address
2. **Environment Variables**: Set up the required environment variables
3. **Docker & Docker Compose**: Installed on your server

## Required Environment Variables

Create a `.env` file in the deployment directory:

```bash
# SSL Configuration
CERTBOT_EMAIL=<EMAIL>

# Database Configuration
DB_HOST=your-db-host
POSTGRES_DB=yezhome
POSTGRES_USER=your-db-user
POSTGRES_PASSWORD=your-db-password

# JWT Configuration
JWT_KEY=your-jwt-key
JWT_ISSUER=your-jwt-issuer
JWT_AUDIENCE=your-jwt-audience

# Internal API JWT Configuration
INTERNAL_JWT_KEY=your-internal-jwt-key
INTERNAL_JWT_ISSUER=your-internal-jwt-issuer
INTERNAL_JWT_AUDIENCE=your-internal-jwt-audience

# AWS Configuration
AWS_S3_ACCESS_KEY=your-s3-access-key
AWS_S3_SECRET_KEY=your-s3-secret-key
AWS_S3_REGION=your-s3-region
AWS_S3_BUCKET_NAME=your-s3-bucket

AWS_SES_ACCESS_KEY=your-ses-access-key
AWS_SES_SECRET_KEY=your-ses-secret-key
AWS_SES_REGION=your-ses-region
AWS_SES_FROM_EMAIL=your-from-email
AWS_SES_FROM_NAME=your-from-name

# Storage Configuration
STORAGE_PROVIDER=S3

# Docker Image Configuration
IMAGE_TAG=latest
COMMIT_SHA=your-commit-sha
```

## Deployment Steps

### 1. Backend Services (APIs)

#### Production Deployment
```bash
cd YEZHome_Deploy
docker-compose -f docker-compose.yml up -d
```

#### Staging Deployment
```bash
cd YEZHome_Deploy
docker-compose -f docker-compose.staging.yml up -d
```

### 2. Frontend Deployment

#### Production
```bash
cd YEZHome_FE
docker-compose -f docker-compose.prod.yml up -d
```

#### Staging
```bash
cd YEZHome_FE
docker-compose -f docker-compose.staging.yml up -d
```

## SSL Certificate Management

### Initial Setup
SSL certificates are automatically generated when the containers start. The process:

1. Nginx starts and serves HTTP traffic
2. Certbot requests SSL certificates from Let's Encrypt
3. Nginx configuration is updated to use HTTPS
4. HTTP traffic is redirected to HTTPS

### Certificate Renewal
Certificates are automatically renewed using cron jobs that run twice daily.

### Manual Certificate Renewal
If needed, you can manually renew certificates:

```bash
# For backend services
docker exec yezhome-certbot-prod certbot renew
docker exec yezhome-nginx-prod nginx -s reload

# For frontend
docker exec yezhome-frontend-prod /usr/local/bin/renew-cert.sh
```

## Monitoring and Troubleshooting

### Check SSL Certificate Status
```bash
# Check certificate expiration
openssl s_client -connect api.yezhome.vn:443 -servername api.yezhome.vn | openssl x509 -noout -dates

# Check certificate details
curl -vI https://api.yezhome.vn
```

### View Logs
```bash
# Backend services
docker logs yezhome-nginx-prod
docker logs yezhome-certbot-prod
docker logs yezhome-api-prod
docker logs yezhome-internal-api-prod

# Frontend
docker logs yezhome-frontend-prod
```

### Common Issues and Solutions

#### 1. Certificate Generation Fails
- Ensure domains point to your server
- Check if ports 80 and 443 are accessible
- Verify CERTBOT_EMAIL is set correctly

#### 2. Nginx Configuration Errors
- Check nginx configuration syntax:
```bash
docker exec yezhome-nginx-prod nginx -t
```

#### 3. SSL Certificate Not Loading
- Verify certificate files exist:
```bash
docker exec yezhome-nginx-prod ls -la /etc/letsencrypt/live/
```

## Security Considerations

1. **Firewall Configuration**: Ensure only necessary ports are open
2. **Regular Updates**: Keep Docker images updated
3. **Monitoring**: Set up monitoring for certificate expiration
4. **Backup**: Backup SSL certificates and configuration files

## File Structure

```
YEZHome_Deploy/
├── docker-compose.yml              # Production backend
├── docker-compose.staging.yml      # Staging backend
├── nginx/
│   ├── nginx.conf                  # Main Nginx config
│   ├── conf.d/
│   │   ├── api.yezhome.vn.conf     # API production config
│   │   └── adapi.yezhome.vn.conf   # Internal API production config
│   └── conf.d-staging/
│       ├── api-test.yezhome.vn.conf     # API staging config
│       └── adapi-test.yezhome.vn.conf   # Internal API staging config

YEZHome_FE/
├── Dockerfile.nginx                # Frontend with Nginx and SSL
├── docker-compose.prod.yml         # Frontend production
├── docker-compose.staging.yml      # Frontend staging
└── nginx/
    ├── nginx.conf                  # Nginx config
    ├── default.conf                # Default server config
    ├── ssl-setup.sh                # SSL setup script
    └── start.sh                    # Container startup script
```

## Health Checks

All services include health checks:
- Backend APIs: HTTP health endpoints
- Frontend: Nginx health check
- SSL: Certificate validity monitoring

## Scaling and Load Balancing

For high-traffic scenarios, consider:
1. Multiple backend instances behind the Nginx proxy
2. Redis for session management
3. CDN for static assets
4. Database read replicas
