name: 🚀 Deploy to Droplet

on:
  repository_dispatch:
    types: [deploy-staging, deploy-production]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        type: choice
        options:
          - staging
          - production
        default: staging
      image_tag:
        description: 'Docker image tag to deploy'
        required: false
        type: string
        default: 'latest'
      reason:
        description: 'Reason for this manual deployment'
        required: true
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: ${{ github.event.client_payload.environment || github.event.inputs.environment }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set deployment variables
        id: vars
        run: |
          # Handle both repository_dispatch and workflow_dispatch triggers
          if [[ "${{ github.event_name }}" == "repository_dispatch" ]]; then
            ENVIRONMENT="${{ github.event.client_payload.environment }}"
            DEPLOY_ENV="${{ github.event.client_payload.deploy_environment }}"
            IMAGE_TAG="${{ github.event.client_payload.image_tag }}"
            COMMIT_SHA="${{ github.event.client_payload.commit_sha }}"
            BUILD_NUMBER="${{ github.event.client_payload.build_number }}"
            AUTO_DEPLOY="${{ github.event.client_payload.auto_deploy }}"
            MANUAL_DEPLOY="${{ github.event.client_payload.manual_deploy }}"
            DEPLOYED_BY="${{ github.event.client_payload.deployed_by }}"
            REASON="${{ github.event.client_payload.reason }}"
          elif [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            ENVIRONMENT="${{ github.event.inputs.environment }}"
            if [[ "${ENVIRONMENT}" == "production" ]]; then
              DEPLOY_ENV="production"
            else
              DEPLOY_ENV="staging"
            fi
            IMAGE_TAG="${{ github.event.inputs.image_tag }}"
            COMMIT_SHA="${{ github.sha }}"
            BUILD_NUMBER="${{ github.run_number }}"
            AUTO_DEPLOY="false"
            MANUAL_DEPLOY="true"
            DEPLOYED_BY="${{ github.actor }}"
            REASON="${{ github.event.inputs.reason }}"
          else
            echo "❌ Unsupported event type: ${{ github.event_name }}"
            exit 1
          fi
          
          echo "ENVIRONMENT=${ENVIRONMENT}" >> $GITHUB_ENV
          echo "DEPLOY_ENV=${DEPLOY_ENV}" >> $GITHUB_ENV
          echo "IMAGE_TAG=${IMAGE_TAG}" >> $GITHUB_ENV
          echo "COMMIT_SHA=${COMMIT_SHA}" >> $GITHUB_ENV
          echo "BUILD_NUMBER=${BUILD_NUMBER}" >> $GITHUB_ENV
          echo "AUTO_DEPLOY=${AUTO_DEPLOY}" >> $GITHUB_ENV
          echo "MANUAL_DEPLOY=${MANUAL_DEPLOY}" >> $GITHUB_ENV
          echo "DEPLOYED_BY=${DEPLOYED_BY}" >> $GITHUB_ENV
          echo "REASON=${REASON}" >> $GITHUB_ENV
          
          # Set deployment paths and compose files
          if [[ "${DEPLOY_ENV}" == "production" ]]; then
            echo "DEPLOY_PATH=/opt/yezhome/yezhome-prod" >> $GITHUB_ENV
            echo "COMPOSE_FILE=docker-compose.yml" >> $GITHUB_ENV
            echo "ENV_FILE_LOCAL=.env.prod" >> $GITHUB_ENV
            echo "ENV_FILE_REMOTE=.env" >> $GITHUB_ENV
          elif [[ "${DEPLOY_ENV}" == "staging" ]]; then
            echo "DEPLOY_PATH=/opt/yezhome/yezhome-staging" >> $GITHUB_ENV
            echo "COMPOSE_FILE=docker-compose.staging.yml" >> $GITHUB_ENV
            echo "ENV_FILE_LOCAL=.env.staging" >> $GITHUB_ENV
            echo "ENV_FILE_REMOTE=.env" >> $GITHUB_ENV
          else
            echo "❌ Unknown environment: ${DEPLOY_ENV}"
            exit 1
          fi

      - name: Create environment file locally
        run: |
          echo "📝 Creating environment file locally..."
          
          if [[ "${{ env.DEPLOY_ENV }}" == "staging" ]]; then
            ENV_FILE="${{ env.ENV_FILE_LOCAL }}"
            echo "# Deployment metadata" > ${ENV_FILE}
            echo "IMAGE_TAG=${{ env.IMAGE_TAG }}" >> ${ENV_FILE}
            echo "BUILD_NUMBER=${{ env.BUILD_NUMBER }}" >> ${ENV_FILE}
            echo "COMMIT_SHA=${{ env.COMMIT_SHA }}" >> ${ENV_FILE}
            echo "LAST_DEPLOYED=$(date -u '+%Y-%m-%d %H:%M:%S UTC')" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            # SSL Configuration
            echo "# SSL Configuration" >> ${ENV_FILE}
            echo "CERTBOT_EMAIL=${{ secrets.CERTBOT_EMAIL }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            echo "# Database Configuration - Staging" >> ${ENV_FILE}
            echo "DB_HOST=${{ vars.DB_HOST }}" >> ${ENV_FILE}
            echo "POSTGRES_DB=${{ vars.POSTGRES_DB }}" >> ${ENV_FILE}
            echo "POSTGRES_USER=${{ secrets.DB_USER }}" >> ${ENV_FILE}
            echo "POSTGRES_PASSWORD=${{ secrets.DB_PASSWORD }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            echo "# JWT Configuration - Staging" >> ${ENV_FILE}
            echo "JWT_KEY=${{ secrets.JWT_KEY }}" >> ${ENV_FILE}
            echo "JWT_ISSUER=${{ vars.JWT_ISSUER }}" >> ${ENV_FILE}
            echo "JWT_AUDIENCE=${{ vars.JWT_AUDIENCE }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            echo "# AWS Configuration - Staging" >> ${ENV_FILE}
            echo "AWS_S3_ACCESS_KEY=${{ secrets.AWS_S3_ACCESS_KEY }}" >> ${ENV_FILE}
            echo "AWS_S3_SECRET_KEY=${{ secrets.AWS_S3_SECRET_KEY }}" >> ${ENV_FILE}
            echo "AWS_S3_REGION=${{ vars.AWS_S3_REGION }}" >> ${ENV_FILE}
            echo "AWS_S3_BUCKET_NAME=${{ vars.AWS_S3_BUCKET_NAME }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}
            
            echo "# AWS SES Configuration - Staging" >> ${ENV_FILE}
            echo "AWS_SES_ACCESS_KEY=${{ secrets.AWS_SES_ACCESS_KEY }}" >> ${ENV_FILE}
            echo "AWS_SES_SECRET_KEY=${{ secrets.AWS_SES_SECRET_KEY }}" >> ${ENV_FILE}
            echo "AWS_SES_REGION=${{ vars.AWS_SES_REGION }}" >> ${ENV_FILE}
            echo "AWS_SES_FROM_EMAIL=${{ vars.AWS_SES_FROM_EMAIL }}" >> ${ENV_FILE}
            echo "AWS_SES_FROM_NAME=${{ vars.AWS_SES_FROM_NAME }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            echo "# Internal API Configuration - Staging" >> ${ENV_FILE}
            echo "INTERNAL_JWT_KEY=${{ secrets.INTERNAL_JWT_KEY }}" >> ${ENV_FILE}
            echo "INTERNAL_JWT_ISSUER=${{ vars.INTERNAL_JWT_ISSUER }}" >> ${ENV_FILE}
            echo "INTERNAL_JWT_AUDIENCE=${{ vars.INTERNAL_JWT_AUDIENCE }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}
                        
          elif [[ "${{ env.DEPLOY_ENV }}" == "production" ]]; then
            ENV_FILE="${{ env.ENV_FILE_LOCAL }}"
            echo "# Deployment metadata" > ${ENV_FILE}
            echo "IMAGE_TAG=${{ env.IMAGE_TAG }}" >> ${ENV_FILE}
            echo "BUILD_NUMBER=${{ env.BUILD_NUMBER }}" >> ${ENV_FILE}
            echo "COMMIT_SHA=${{ env.COMMIT_SHA }}" >> ${ENV_FILE}
            echo "LAST_DEPLOYED=$(date -u '+%Y-%m-%d %H:%M:%S UTC')" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            # SSL Configuration
            echo "# SSL Configuration" >> ${ENV_FILE}
            echo "CERTBOT_EMAIL=${{ secrets.CERTBOT_EMAIL }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            echo "# Database Configuration - Production" >> ${ENV_FILE}
            echo "DB_HOST=${{ vars.DB_HOST }}" >> ${ENV_FILE}
            echo "POSTGRES_DB=${{ vars.POSTGRES_DB }}" >> ${ENV_FILE}
            echo "POSTGRES_USER=${{ secrets.DB_USER }}" >> ${ENV_FILE}
            echo "POSTGRES_PASSWORD=${{ secrets.DB_PASSWORD }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            echo "# JWT Configuration - Production" >> ${ENV_FILE}
            echo "JWT_KEY=${{ secrets.JWT_KEY }}" >> ${ENV_FILE}
            echo "JWT_ISSUER=${{ vars.JWT_ISSUER }}" >> ${ENV_FILE}
            echo "JWT_AUDIENCE=${{ vars.JWT_AUDIENCE }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            echo "# AWS Configuration - Production" >> ${ENV_FILE}
            echo "AWS_S3_ACCESS_KEY=${{ secrets.AWS_S3_ACCESS_KEY }}" >> ${ENV_FILE}
            echo "AWS_S3_SECRET_KEY=${{ secrets.AWS_S3_SECRET_KEY }}" >> ${ENV_FILE}
            echo "AWS_S3_REGION=${{ vars.AWS_S3_REGION }}" >> ${ENV_FILE} 
            echo "AWS_S3_BUCKET_NAME=${{ vars.AWS_S3_BUCKET_NAME }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            echo "# AWS SES Configuration - Production" >> ${ENV_FILE}
            echo "AWS_SES_ACCESS_KEY=${{ secrets.AWS_SES_ACCESS_KEY }}" >> ${ENV_FILE}
            echo "AWS_SES_SECRET_KEY=${{ secrets.AWS_SES_SECRET_KEY }}" >> ${ENV_FILE}
            echo "AWS_SES_REGION=${{ vars.AWS_SES_REGION }}" >> ${ENV_FILE}
            echo "AWS_SES_FROM_EMAIL=${{ vars.AWS_SES_FROM_EMAIL }}" >> ${ENV_FILE}
            echo "AWS_SES_FROM_NAME=${{ vars.AWS_SES_FROM_NAME }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            echo "# Internal API Configuration - Production" >> ${ENV_FILE}
            echo "INTERNAL_JWT_KEY=${{ secrets.INTERNAL_JWT_KEY }}" >> ${ENV_FILE}
            echo "INTERNAL_JWT_ISSUER=${{ vars.INTERNAL_JWT_ISSUER }}" >> ${ENV_FILE}
            echo "INTERNAL_JWT_AUDIENCE=${{ vars.INTERNAL_JWT_AUDIENCE }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}
          fi
          
          # Add manual deployment metadata if applicable
          if [[ "${{ env.MANUAL_DEPLOY }}" == "true" ]]; then
            ENV_FILE="${{ env.ENV_FILE_LOCAL }}"
            echo "DEPLOYED_BY=${{ env.DEPLOYED_BY }}" >> ${ENV_FILE}
          fi

      - name: Create deployment directory on Droplet
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          script: |
            echo "📁 Creating deployment directory: ${{ env.DEPLOY_PATH }}"
            mkdir -p ${{ env.DEPLOY_PATH }}
            echo "✅ Directories created successfully"

      - name: Copy files to Droplet
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          source: "Dockerfile,${{ env.COMPOSE_FILE }},${{ env.ENV_FILE_LOCAL }}"
          target: ${{ env.DEPLOY_PATH }}
          
      - name: Rename environment file on Droplet
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          script: |
            cd ${{ env.DEPLOY_PATH }}
            mv ${{ env.ENV_FILE_LOCAL }} ${{ env.ENV_FILE_REMOTE }}
            echo "✅ Environment file renamed"

      - name: SSH to Droplet and deploy
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          timeout: 600s
          command_timeout: 15m
          script: |
            echo "🚀 Deploying to ${{ env.ENVIRONMENT }} environment..."
            echo "📁 Deploy path: ${{ env.DEPLOY_PATH }}"
            echo "🐳 Image tag: ${{ env.IMAGE_TAG }}"
            echo "📄 Compose file: ${{ env.COMPOSE_FILE }}"
            
            # Navigate to deployment directory
            cd ${{ env.DEPLOY_PATH }} || { echo "❌ Failed to change directory"; exit 1; }
            
            # Set secure permissions
            chmod 600 ${{ env.ENV_FILE_REMOTE }}
            
            echo "🔐 Logging into GitHub Container Registry..."
            echo "${{ secrets.PULL_PAT }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin
            
            echo "🔄 Pulling latest images..."
            docker compose -f ${{ env.COMPOSE_FILE }} pull || { echo "❌ Failed to pull images"; exit 1; }
            
            echo "🛑 Stopping existing services..."
            docker compose -f ${{ env.COMPOSE_FILE }} down
            
            echo "🚀 Starting ${{ env.ENVIRONMENT }} services..."
            docker compose -f ${{ env.COMPOSE_FILE }} up -d --build || { echo "❌ Failed to start services"; exit 1; }

            echo "⏳ Services started. Waiting 15 seconds for network to stabilize..."
            sleep 15

            # --- Health & SSL check ---
            # Step 1: Dynamically set variables for the correct environment
            if [[ "${{ env.DEPLOY_ENV }}" == "production" ]]; then
              DOMAINS="api.yezhome.vn adapi.yezhome.vn"
              PORT=443
              CONTAINER="yezhome-nginx-prod"
            else
              DOMAINS="api-test.yezhome.vn adapi-test.yezhome.vn"
              PORT=8443
              CONTAINER="yezhome-nginx-staging"
            fi
            
            echo "⏳ Health & SSL check for ${{ env.DEPLOY_ENV }}..."
            ALL_OK=true
            
            for DOMAIN in $DOMAINS; do
              echo "  -> Checking ${DOMAIN}:${PORT} ..."
              
              # Quick check
              if ! timeout 30 bash -c "until curl -k -sSfL --resolve ${DOMAIN}:${PORT}:127.0.0.1 https://${DOMAIN}:${PORT}/healthz; do sleep 5; done"; then
                echo "⚠️ Quick check failed for $DOMAIN, trying extended check..."

                if ! timeout 300 bash -c "until curl -k -sSfL --resolve ${DOMAIN}:${PORT}:127.0.0.1 https://${DOMAIN}:${PORT}/healthz; do sleep 10; done"; then
                  echo "❌ $DOMAIN failed health check!"
                  echo "💡 Last curl verbose output:"
                  curl --verbose --resolve ${DOMAIN}:${PORT}:127.0.0.1 https://${DOMAIN}:${PORT}/healthz || true
                  ALL_OK=false
                fi
              fi

              if [ "$ALL_OK" = "true" ]; then
                echo "✅ $DOMAIN is healthy."
              fi
            done

            if [ "$ALL_OK" = "false" ]; then
              echo "--- Fetching logs from $CONTAINER for debugging ---"
              docker logs $CONTAINER || echo "Could not fetch logs."
              exit 1
            fi

            echo "✅ All domains healthy and SSL OK."
            
            echo "🔍 Checking service status..."
            docker compose -f ${{ env.COMPOSE_FILE }} ps
            
            # Clean up
            echo "🧹 Cleaning up old images..."
            docker image prune -f
            
            echo "🔐 Logging out of GitHub Container Registry..."
            docker logout ghcr.io
            
            echo "✅ ${{ env.ENVIRONMENT }} deployment completed successfully!"
            echo ""
            echo "📊 Deployment Summary:"
            echo "  Environment: ${{ env.ENVIRONMENT }}"
            echo "  Image Tag: ${{ env.IMAGE_TAG }}"
            if [[ "${{ env.DEPLOY_ENV }}" == "production" ]]; then
              echo "  API URL: https://api.yezhome.vn"
              echo "  Internal API URL: https://adapi.yezhome.vn"
            else
              echo "  API URL: https://api-test.yezhome.vn"
              echo "  Internal API URL: https://adapi-test.yezhome.vn"
            fi
            echo "  Completed: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"
