name: 🚀 Deploy to Droplet without Nginx & SSL

on:
  repository_dispatch:
    types: [deploy-staging, deploy-production]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        type: choice
        options:
          - staging
          - production
        default: staging
      image_tag:
        description: 'Docker image tag to deploy (e.g., "latest", "build-123", or commit SHA)'
        required: false
        type: string
        default: 'latest'
      reason:
        description: 'Reason for this manual deployment'
        required: true
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: ${{ github.event.client_payload.environment || github.event.inputs.environment }}  # Uses "staging" or "production"
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set deployment variables
        id: vars
        run: |
          # Handle both repository_dispatch and workflow_dispatch triggers
          if [[ "${{ github.event_name }}" == "repository_dispatch" ]]; then
            # Extract environment info from client payload (repository_dispatch)
            ENVIRONMENT="${{ github.event.client_payload.environment }}"
            DEPLOY_ENV="${{ github.event.client_payload.deploy_environment }}"
            IMAGE_TAG="${{ github.event.client_payload.image_tag }}"
            COMMIT_SHA="${{ github.event.client_payload.commit_sha }}"
            BUILD_NUMBER="${{ github.event.client_payload.build_number }}"
            AUTO_DEPLOY="${{ github.event.client_payload.auto_deploy }}"
            MANUAL_DEPLOY="${{ github.event.client_payload.manual_deploy }}"
            DEPLOYED_BY="${{ github.event.client_payload.deployed_by }}"
            REASON="${{ github.event.client_payload.reason }}"
          elif [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            # Extract environment info from inputs (workflow_dispatch)
            ENVIRONMENT="${{ github.event.inputs.environment }}"
            if [[ "${ENVIRONMENT}" == "production" ]]; then
              DEPLOY_ENV="production"
            else
              DEPLOY_ENV="staging"
            fi
            IMAGE_TAG="${{ github.event.inputs.image_tag }}"
            COMMIT_SHA="${{ github.sha }}"
            BUILD_NUMBER="${{ github.run_number }}"
            AUTO_DEPLOY="false"
            MANUAL_DEPLOY="true"
            DEPLOYED_BY="${{ github.actor }}"
            REASON="${{ github.event.inputs.reason }}"
          else
            echo "❌ Unsupported event type: ${{ github.event_name }}"
            exit 1
          fi
          
          echo "ENVIRONMENT=${ENVIRONMENT}" >> $GITHUB_ENV
          echo "DEPLOY_ENV=${DEPLOY_ENV}" >> $GITHUB_ENV
          echo "IMAGE_TAG=${IMAGE_TAG}" >> $GITHUB_ENV
          echo "COMMIT_SHA=${COMMIT_SHA}" >> $GITHUB_ENV
          echo "BUILD_NUMBER=${BUILD_NUMBER}" >> $GITHUB_ENV
          echo "AUTO_DEPLOY=${AUTO_DEPLOY}" >> $GITHUB_ENV
          echo "MANUAL_DEPLOY=${MANUAL_DEPLOY}" >> $GITHUB_ENV
          echo "DEPLOYED_BY=${DEPLOYED_BY}" >> $GITHUB_ENV
          echo "REASON=${REASON}" >> $GITHUB_ENV
          
          # Set deployment paths and compose files
          if [[ "${DEPLOY_ENV}" == "production" ]]; then
            echo "DEPLOY_PATH=/opt/yezhome/yezhome-prod" >> $GITHUB_ENV
            echo "COMPOSE_FILE=docker-compose.yml" >> $GITHUB_ENV
            echo "ENV_FILE_LOCAL=.env.prod" >> $GITHUB_ENV
            echo "ENV_FILE_REMOTE=.env" >> $GITHUB_ENV
          elif [[ "${DEPLOY_ENV}" == "staging" ]]; then
            echo "DEPLOY_PATH=/opt/yezhome/yezhome-staging" >> $GITHUB_ENV
            echo "COMPOSE_FILE=docker-compose.staging.yml" >> $GITHUB_ENV
            echo "ENV_FILE_LOCAL=.env.staging" >> $GITHUB_ENV
            echo "ENV_FILE_REMOTE=.env" >> $GITHUB_ENV
          else
            echo "❌ Unknown environment: ${DEPLOY_ENV}"
            exit 1
          fi

      - name: Create environment file locally
        run: |
          echo "📝 Creating environment file locally..."
          echo "🔍 DEPLOY_ENV: ${{ env.DEPLOY_ENV }}"
          echo "🔍 ENV_FILE_LOCAL: ${{ env.ENV_FILE_LOCAL }}"
          echo "🔍 Current directory: $(pwd)"
          echo "🔍 Files before creation: $(ls -la)"
          
          if [[ "${{ env.DEPLOY_ENV }}" == "staging" ]]; then
            # Create staging environment file
            ENV_FILE="${{ env.ENV_FILE_LOCAL }}"
            echo "📝 Creating staging environment file: ${ENV_FILE}"
            echo "# Deployment metadata" > ${ENV_FILE}
            echo "IMAGE_TAG=${{ env.IMAGE_TAG }}" >> ${ENV_FILE}
            echo "BUILD_NUMBER=${{ env.BUILD_NUMBER }}" >> ${ENV_FILE}
            echo "COMMIT_SHA=${{ env.COMMIT_SHA }}" >> ${ENV_FILE}
            echo "LAST_DEPLOYED=$(date -u '+%Y-%m-%d %H:%M:%S UTC')" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            echo "# Database Configuration - Staging" >> ${ENV_FILE}
            echo "DB_HOST=${{ vars.DB_HOST }}" >> ${ENV_FILE}
            echo "POSTGRES_DB=${{ vars.POSTGRES_DB }}" >> ${ENV_FILE}
            echo "POSTGRES_USER=${{ secrets.DB_USER }}" >> ${ENV_FILE}
            echo "POSTGRES_PASSWORD=${{ secrets.DB_PASSWORD }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            echo "# JWT Configuration - Staging" >> ${ENV_FILE}
            echo "JWT_KEY=${{ secrets.JWT_KEY }}" >> ${ENV_FILE}
            echo "JWT_ISSUER=${{ vars.JWT_ISSUER }}" >> ${ENV_FILE}
            echo "JWT_AUDIENCE=${{ vars.JWT_AUDIENCE }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            echo "# AWS Configuration - Staging" >> ${ENV_FILE}
            echo "AWS_S3_ACCESS_KEY=${{ secrets.AWS_S3_ACCESS_KEY }}" >> ${ENV_FILE}
            echo "AWS_S3_SECRET_KEY=${{ secrets.AWS_S3_SECRET_KEY }}" >> ${ENV_FILE}
            echo "AWS_S3_REGION=${{ vars.AWS_S3_REGION }}" >> ${ENV_FILE}
            echo "AWS_S3_BUCKET_NAME=${{ vars.AWS_S3_BUCKET_NAME }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}
            
            echo "# AWS SES Configuration - Staging" >> ${ENV_FILE}
            echo "AWS_SES_ACCESS_KEY=${{ secrets.AWS_SES_ACCESS_KEY }}" >> ${ENV_FILE}
            echo "AWS_SES_SECRET_KEY=${{ secrets.AWS_SES_SECRET_KEY }}" >> ${ENV_FILE}
            echo "AWS_SES_REGION=${{ vars.AWS_SES_REGION }}" >> ${ENV_FILE}
            echo "AWS_SES_FROM_EMAIL=${{ vars.AWS_SES_FROM_EMAIL }}" >> ${ENV_FILE}
            echo "AWS_SES_FROM_NAME=${{ vars.AWS_SES_FROM_NAME }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            echo "# Internal API Configuration - Staging" >> ${ENV_FILE}
            echo "INTERNAL_JWT_KEY=${{ secrets.INTERNAL_JWT_KEY }}" >> ${ENV_FILE}
            echo "INTERNAL_JWT_ISSUER=${{ vars.INTERNAL_JWT_ISSUER }}" >> ${ENV_FILE}
            echo "INTERNAL_JWT_AUDIENCE=${{ vars.INTERNAL_JWT_AUDIENCE }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}
                        
          elif [[ "${{ env.DEPLOY_ENV }}" == "production" ]]; then
            # Create production environment file
            ENV_FILE="${{ env.ENV_FILE_LOCAL }}"
            echo "📝 Creating production environment file: ${ENV_FILE}"
            echo "# Deployment metadata" > ${ENV_FILE}
            echo "IMAGE_TAG=${{ env.IMAGE_TAG }}" >> ${ENV_FILE}
            echo "BUILD_NUMBER=${{ env.BUILD_NUMBER }}" >> ${ENV_FILE}
            echo "COMMIT_SHA=${{ env.COMMIT_SHA }}" >> ${ENV_FILE}
            echo "LAST_DEPLOYED=$(date -u '+%Y-%m-%d %H:%M:%S UTC')" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            echo "# Database Configuration - Production" >> ${ENV_FILE}
            echo "DB_HOST=${{ vars.DB_HOST }}" >> ${ENV_FILE}
            echo "POSTGRES_DB=${{ vars.POSTGRES_DB }}" >> ${ENV_FILE}
            echo "POSTGRES_USER=${{ secrets.DB_USER }}" >> ${ENV_FILE}
            echo "POSTGRES_PASSWORD=${{ secrets.DB_PASSWORD }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            echo "# JWT Configuration - Production" >> ${ENV_FILE}
            echo "JWT_KEY=${{ secrets.JWT_KEY }}" >> ${ENV_FILE}
            echo "JWT_ISSUER=${{ vars.JWT_ISSUER }}" >> ${ENV_FILE}
            echo "JWT_AUDIENCE=${{ vars.JWT_AUDIENCE }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            echo "# AWS Configuration - Production" >> ${ENV_FILE}
            echo "AWS_S3_ACCESS_KEY=${{ secrets.AWS_S3_ACCESS_KEY }}" >> ${ENV_FILE}
            echo "AWS_S3_SECRET_KEY=${{ secrets.AWS_S3_SECRET_KEY }}" >> ${ENV_FILE}
            echo "AWS_S3_REGION=${{ vars.AWS_S3_REGION }}" >> ${ENV_FILE} 
            echo "AWS_S3_BUCKET_NAME=${{ vars.AWS_S3_BUCKET_NAME }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            echo "# AWS SES Configuration - Production" >> ${ENV_FILE}
            echo "AWS_SES_ACCESS_KEY=${{ secrets.AWS_SES_ACCESS_KEY }}" >> ${ENV_FILE}
            echo "AWS_SES_SECRET_KEY=${{ secrets.AWS_SES_SECRET_KEY }}" >> ${ENV_FILE}
            echo "AWS_SES_REGION=${{ vars.AWS_SES_REGION }}" >> ${ENV_FILE}
            echo "AWS_SES_FROM_EMAIL=${{ vars.AWS_SES_FROM_EMAIL }}" >> ${ENV_FILE}
            echo "AWS_SES_FROM_NAME=${{ vars.AWS_SES_FROM_NAME }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}

            echo "# Internal API Configuration - Production" >> ${ENV_FILE}
            echo "INTERNAL_JWT_KEY=${{ secrets.INTERNAL_JWT_KEY }}" >> ${ENV_FILE}
            echo "INTERNAL_JWT_ISSUER=${{ vars.INTERNAL_JWT_ISSUER }}" >> ${ENV_FILE}
            echo "INTERNAL_JWT_AUDIENCE=${{ vars.INTERNAL_JWT_AUDIENCE }}" >> ${ENV_FILE}
            echo "" >> ${ENV_FILE}
          fi
          
          # Add manual deployment metadata if applicable
          if [[ "${{ env.MANUAL_DEPLOY }}" == "true" ]]; then
            ENV_FILE="${{ env.ENV_FILE_LOCAL }}"
            echo "DEPLOYED_BY=${{ env.DEPLOYED_BY }}" >> ${ENV_FILE}
          fi
          
          # Verify file creation
          ENV_FILE="${{ env.ENV_FILE_LOCAL }}"
          echo "🔍 Files after creation: $(ls -la)"
          if [[ -f "${ENV_FILE}" ]]; then
            echo "✅ Environment file created successfully: ${ENV_FILE}"
            echo "📄 File size: $(wc -l < ${ENV_FILE}) lines"
            echo "📄 File content preview:"
            head -20 ${ENV_FILE}
          else
            echo "❌ Environment file NOT created: ${ENV_FILE}"
            exit 1
          fi

      - name: Create deployment directory on Droplet
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          script: |
            echo "📁 Creating deployment directory: ${{ env.DEPLOY_PATH }}"
            mkdir -p ${{ env.DEPLOY_PATH }}
            echo "✅ Directory created successfully"

      - name: Copy files to Droplet
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          source: "${{ env.COMPOSE_FILE }},${{ env.ENV_FILE_LOCAL }}"
          target: ${{ env.DEPLOY_PATH }}
          
      - name: Rename environment file on Droplet
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          script: |
            cd ${{ env.DEPLOY_PATH }}
            # Rename the environment file to the standard .env name that Docker Compose expects
            mv ${{ env.ENV_FILE_LOCAL }} ${{ env.ENV_FILE_REMOTE }}
            echo "✅ Environment file renamed from ${{ env.ENV_FILE_LOCAL }} to ${{ env.ENV_FILE_REMOTE }}"

      - name: SSH to Droplet and deploy
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          timeout: 300s
          command_timeout: 10m
          script: |
            echo "🚀 Deploying to ${{ env.ENVIRONMENT }} environment..."
            echo "📁 Deploy path: ${{ env.DEPLOY_PATH }}"
            echo "🐳 Image tag: ${{ env.IMAGE_TAG }}"
            echo "📄 Compose file: ${{ env.COMPOSE_FILE }}"
            
            # Display deployment context
            if [[ "${{ env.AUTO_DEPLOY }}" == "true" ]]; then
              echo "🤖 Deployment type: Automatic (triggered by push to main)"
            elif [[ "${{ env.MANUAL_DEPLOY }}" == "true" ]]; then
              echo "👤 Deployment type: Manual (triggered by ${{ env.DEPLOYED_BY }})"
              echo "💭 Reason: ${{ env.REASON }}"
            fi
            
            if [[ -n "${{ env.BUILD_NUMBER }}" ]]; then
              echo "🏗️  Build number: ${{ env.BUILD_NUMBER }}"
            fi
            
            if [[ -n "${{ env.COMMIT_SHA }}" ]]; then
              echo "📝 Commit: ${{ env.COMMIT_SHA }}"
            fi
            
            # Navigate to deployment directory
            cd ${{ env.DEPLOY_PATH }} || { echo "❌ Failed to change directory to ${{ env.DEPLOY_PATH }}"; exit 1; }
            
            # Set secure permissions on environment file
            chmod 600 ${{ env.ENV_FILE_REMOTE }}
            
            echo "✅ Files copied and permissions set"
            
            echo "🔐 Logging into GitHub Container Registry..."
            echo "${{ secrets.PULL_PAT }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin
            
            echo "🔄 Pulling latest images..."
            if ! docker compose -f ${{ env.COMPOSE_FILE }} pull; then
              echo "❌ Failed to pull Docker images"
              exit 1
            fi
            
            echo "🛑 Stopping existing services..."
            docker compose -f ${{ env.COMPOSE_FILE }} down
            
            echo "🚀 Starting ${{ env.ENVIRONMENT }} services..."
            if ! docker compose -f ${{ env.COMPOSE_FILE }} up -d; then
              echo "❌ Failed to start services"
              exit 1
            fi
            
            echo "⏳ Waiting for services to be ready..."
            sleep 10
            
            echo "🔍 Checking service status..."
            docker compose -f ${{ env.COMPOSE_FILE }} ps
            
            echo "🧹 Cleaning up old images..."
            docker image prune -f
            
            echo "🔐 Logging out of GitHub Container Registry..."
            docker logout ghcr.io
            
            # Final status and summary
            echo "✅ ${{ env.ENVIRONMENT }} deployment completed successfully!"
            echo ""
            echo "📊 Deployment Summary:"
            echo "  Environment: ${{ env.ENVIRONMENT }}"
            echo "  Image Tag: ${{ env.IMAGE_TAG }}"
            if [[ -n "${{ env.BUILD_NUMBER }}" ]]; then
              echo "  Build: #${{ env.BUILD_NUMBER }}"
            fi
            if [[ -n "${{ env.COMMIT_SHA }}" ]]; then
              echo "  Commit: ${{ env.COMMIT_SHA }}"
            fi
            if [[ "${{ env.MANUAL_DEPLOY }}" == "true" ]]; then
              echo "  Deployed by: ${{ env.DEPLOYED_BY }}"
              echo "  Reason: ${{ env.REASON }}"
            fi
            echo "  Completed: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"
