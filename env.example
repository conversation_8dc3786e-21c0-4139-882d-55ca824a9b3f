# YEZHome Environment Configuration
# Copy this file to .env and update the values

# SSL Configuration
CERTBOT_EMAIL=<EMAIL>

# Database Configuration
DB_HOST=your-database-host
POSTGRES_DB=yezhome
POSTGRES_USER=your-db-username
POSTGRES_PASSWORD=your-strong-db-password

# JWT Configuration for Main API
JWT_KEY=your-very-long-and-secure-jwt-key-here
JWT_ISSUER=https://api.yezhome.vn
JWT_AUDIENCE=https://yezhome.vn

# JWT Configuration for Internal API
INTERNAL_JWT_KEY=your-very-long-and-secure-internal-jwt-key-here
INTERNAL_JWT_ISSUER=https://adapi.yezhome.vn
INTERNAL_JWT_AUDIENCE=https://yezhome.vn

# AWS S3 Configuration
AWS_S3_ACCESS_KEY=your-s3-access-key
AWS_S3_SECRET_KEY=your-s3-secret-key
AWS_S3_REGION=ap-southeast-1
AWS_S3_BUCKET_NAME=your-s3-bucket-name

# AWS SES Configuration
AWS_SES_ACCESS_KEY=your-ses-access-key
AWS_SES_SECRET_KEY=your-ses-secret-key
AWS_SES_REGION=ap-southeast-1
AWS_SES_FROM_EMAIL=<EMAIL>
AWS_SES_FROM_NAME=YEZHome

# Storage Configuration
STORAGE_PROVIDER=S3

# Docker Image Configuration
IMAGE_TAG=latest
COMMIT_SHA=latest

# Optional: Custom configuration
# CERTBOT_STAGING=false  # Set to true for testing with Let's Encrypt staging
