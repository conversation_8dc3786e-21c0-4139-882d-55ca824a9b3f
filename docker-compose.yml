# docker-compose.yml
services:
  nginx:
    build: .
    container_name: yezhome-nginx-prod
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl-init.sh:/usr/local/bin/ssl-init.sh:ro
      - certbot_certs:/etc/letsencrypt
      - certbot_www:/var/www/certbot
    environment:
      - CERTBOT_EMAIL=${CERTBOT_EMAIL}
      - CERTBOT_STAGING=false
      - DOMAINS=api.yezhome.vn,adapi.yezhome.vn
    networks:
      - yezhome-prod-network
    depends_on:
      - yezhome-api-prod
      - yezhome-internal-api-prod
    command: >
      sh -c "
        /usr/local/bin/ssl-init.sh &&
        crond -f -L /var/log/cron.log &
        nginx -g 'daemon off;'
      "
    labels:
      - "environment=production"

  yezhome-api-prod:
    image: ghcr.io/gbnguyen1340/yezhome-api:${IMAGE_TAG:-latest}
    container_name: yezhome-api-prod
    restart: always
    expose:
      - "8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__YezHomeConnection=Host=${DB_HOST};Database=${POSTGRES_DB};Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      - JWT__Key=${JWT_KEY}
      - JWT__Issuer=${JWT_ISSUER}
      - JWT__Audience=${JWT_AUDIENCE}
      - AWS__S3__AccessKey=${AWS_S3_ACCESS_KEY}
      - AWS__S3__SecretKey=${AWS_S3_SECRET_KEY}
      - AWS__S3__Region=${AWS_S3_REGION}
      - AWS__S3__BucketName=${AWS_S3_BUCKET_NAME}
      - AWS__SES__AccessKey=${AWS_SES_ACCESS_KEY}
      - AWS__SES__SecretKey=${AWS_SES_SECRET_KEY}
      - AWS__SES__Region=${AWS_SES_REGION}
      - AWS__SES__FromEmail=${AWS_SES_FROM_EMAIL}
      - AWS__SES__FromName=${AWS_SES_FROM_NAME}
      - Storage__Provider=${STORAGE_PROVIDER:-S3}
    volumes:
      - api_property_images_prod:/app/PropertyImages
      - api_user_avatars_prod:/app/UserAvatars
      - api_temp_prod:/app/Temp
    networks:
      - yezhome-prod-network
    labels:
      - "environment=production"
      - "commit_sha=${COMMIT_SHA:-unknown}"

  yezhome-internal-api-prod:
    image: ghcr.io/gbnguyen1340/yezhome-internal-api:${IMAGE_TAG:-latest}
    container_name: yezhome-internal-api-prod
    restart: always
    expose:
      - "8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__YezHomeConnection=Host=${DB_HOST};Database=${POSTGRES_DB};Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      - JWT__Key=${INTERNAL_JWT_KEY}
      - JWT__Issuer=${INTERNAL_JWT_ISSUER}
      - JWT__Audience=${INTERNAL_JWT_AUDIENCE}
      - AWS__SES__AccessKey=${AWS_SES_ACCESS_KEY}
      - AWS__SES__SecretKey=${AWS_SES_SECRET_KEY}
      - AWS__SES__Region=${AWS_SES_REGION}
      - AWS__SES__FromEmail=${AWS_SES_FROM_EMAIL}
      - AWS__SES__FromName=${AWS_SES_FROM_NAME}
    networks:
      - yezhome-prod-network
    labels:
      - "environment=production"
      - "commit_sha=${COMMIT_SHA:-unknown}"

volumes:
  api_property_images_prod:
    name: yezhome_prod_property_images
  api_user_avatars_prod:
    name: yezhome_prod_user_avatars
  api_temp_prod:
    name: yezhome_prod_temp
  certbot_certs:
    name: yezhome_prod_certbot_certs
  certbot_www:
    name: yezhome_prod_certbot_www

networks:
  yezhome-prod-network:
    name: yezhome-prod-network
    driver: bridge
