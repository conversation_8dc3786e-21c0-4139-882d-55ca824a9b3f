name: '🔩 Deploy Nginx Config'

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        type: choice
        options:
          - staging
          - production
        default: staging
      reason:
        description: 'Reason for this Nginx deployment'
        required: true
        type: string

jobs:
  deploy-nginx-config:
    name: Deploy Nginx to ${{ github.event.inputs.environment }}
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set deployment variables
        id: vars
        run: |
          ENVIRONMENT="${{ github.event.inputs.environment }}"
          if [[ "${ENVIRONMENT}" == "production" ]]; then
            echo "DEPLOY_PATH=/opt/yezhome/yezhome-prod" >> $GITHUB_ENV
            echo "NGINX_CONTAINER=yezhome-nginx-prod" >> $GITHUB_ENV
          elif [[ "${ENVIRONMENT}" == "staging" ]]; then
            echo "DEPLOY_PATH=/opt/yezhome/yezhome-staging" >> $GITHUB_ENV
            echo "NGINX_CONTAINER=yezhome-nginx-staging" >> $GITHUB_ENV
          else
            echo "❌ Unknown environment: ${ENVIRONMENT}"
            exit 1
          fi

      - name: Copy Nginx files to Droplet
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          source: "nginx/"
          target: ${{ env.DEPLOY_PATH }}
          strip_components: 1 # Loại bỏ thư mục 'nginx' ở thư mục đích

      - name: Reload Nginx on Droplet
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          script: |
            echo "🔄 Reloading Nginx configuration for container: ${{ env.NGINX_CONTAINER }}"
            docker exec ${{ env.NGINX_CONTAINER }} nginx -s reload
            echo "✅ Nginx reloaded successfully!"