# YEZHome Comprehensive Deployment Guide

This is the complete deployment guide for YEZHome with automatic SSL certificates, Nginx reverse proxy, and multi-environment support.

## 🏗️ Architecture Overview

### **Domain Mapping**

#### Production
- **Website Frontend**: `yezhome.vn` (HTTPS with SSL)
- **API**: `api.yezhome.vn` (HTTPS with SSL)
- **Internal API**: `adapi.yezhome.vn` (HTTPS with SSL)

#### Staging
- **Website Frontend**: `test.yezhome.vn` (HTTPS with SSL)
- **API**: `api-test.yezhome.vn` (HTTPS with SSL)
- **Internal API**: `adapi-test.yezhome.vn` (HTTPS with SSL)

### **Infrastructure Components**

```
┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend APIs  │
│   (Next.js)     │    │   (.NET Core)   │
│                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │   Nginx     │ │    │ │   Nginx     │ │
│ │ + SSL/TLS   │ │    │ │ + SSL/TLS   │ │
│ │ + Certbot   │ │    │ │ + Certbot   │ │
│ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘
        │                       │
        └───────────────────────┘
                    │
        ┌─────────────────┐
        │   PostgreSQL    │
        │   Database      │
        └─────────────────┘
```

### **Environment Structure**
```
/opt/yezhome/
├── yezhome-prod/          # Production environment
│   ├── docker-compose.yml # Production backend services
│   ├── .env               # Production environment variables
│   └── nginx/             # Nginx configurations
├── yezhome-staging/       # Staging environment
│   ├── docker-compose.staging.yml
│   ├── .env               # Staging environment variables
│   └── nginx/             # Nginx configurations
└── yezhome-db/            # Shared database (existing)
    ├── docker-compose.yml
    └── .env
```

## 🔄 Deployment Workflows

### **Workflow Options**

#### **Option 1: Single Branch Workflow (Recommended)**
```
Push to 'main' → Build images → Auto-deploy to Staging → Manual Deploy to Production
```

#### **Option 2: Multi-Branch Workflow**
```
Push to 'staging' → Deploy to Staging
Push to 'main' → Deploy to Production
```

### **Deployment Methods**

1. **Automated via GitHub Actions** (Recommended)
2. **Manual via deployment script**
3. **Direct docker-compose commands**

## 🚀 Quick Start Deployment

### **Prerequisites**

1. **Domain Configuration**: Ensure domains point to your server IP
2. **GitHub Secrets**: Configure required secrets (see below)
3. **Docker & Docker Compose**: Installed on server
4. **Ports**: 80, 443 open for SSL certificate generation

### **Required GitHub Secrets**

Add these to your GitHub repository settings:

```bash
# Server Access
DROPLET_IP=your.server.ip.address
DROPLET_USERNAME=root
DROPLET_SSH_KEY=your-private-ssh-key

# SSL Configuration
CERTBOT_EMAIL=<EMAIL>

# Database
DB_USER=postgres
DB_PASSWORD=your-secure-db-password

# JWT Keys
JWT_KEY=your-32-char-minimum-jwt-key
INTERNAL_JWT_KEY=your-32-char-minimum-internal-jwt-key

# AWS Configuration
AWS_S3_ACCESS_KEY=your-s3-access-key
AWS_S3_SECRET_KEY=your-s3-secret-key
AWS_SES_ACCESS_KEY=your-ses-access-key
AWS_SES_SECRET_KEY=your-ses-secret-key

# Container Registry
PULL_PAT=your-github-personal-access-token
```

### **Required GitHub Variables**

Add these to your repository variables:

```bash
# Database
DB_HOST=yezhome-postgres
POSTGRES_DB=yezhome

# JWT Configuration
JWT_ISSUER=https://api.yezhome.vn
JWT_AUDIENCE=https://yezhome.vn
INTERNAL_JWT_ISSUER=https://adapi.yezhome.vn
INTERNAL_JWT_AUDIENCE=https://yezhome.vn

# AWS Configuration
AWS_S3_REGION=ap-southeast-1
AWS_S3_BUCKET_NAME=yezhome-media
AWS_SES_REGION=ap-southeast-1
AWS_SES_FROM_EMAIL=<EMAIL>
AWS_SES_FROM_NAME=YEZHome
```

## 📋 Automated Deployment (GitHub Actions)

### **Backend Deployment**

#### **Automatic Staging Deployment**
1. **Push to main branch**:
   ```bash
   git add .
   git commit -m "feat: add new feature"
   git push origin main
   ```

2. **Automatic Process**:
   - ✅ Builds Docker images
   - ✅ Pushes to GitHub Container Registry
   - ✅ Auto-deploys to staging
   - ✅ Generates SSL certificates automatically
   - ✅ Configures Nginx reverse proxy

3. **Access Staging**:
   - API: `https://api-test.yezhome.vn`
   - Internal API: `https://adapi-test.yezhome.vn`

#### **Manual Production Deployment**
1. **Navigate to GitHub Actions**:
   - Go to `YEZHome_Deploy` repository → Actions tab
   - Click "🚀 Deploy to Droplet with SSL" workflow

2. **Click "Run workflow"**:
   - **Environment**: `production`
   - **Image tag**: Leave empty for latest
   - **Reason**: Explain deployment reason

3. **Automatic Process**:
   - ✅ Pulls latest images
   - ✅ Deploys with SSL support
   - ✅ Generates SSL certificates for production domains
   - ✅ Configures production Nginx

4. **Access Production**:
   - API: `https://api.yezhome.vn`
   - Internal API: `https://adapi.yezhome.vn`

### **Frontend Deployment**

#### **Staging Frontend**
1. **Push to main** (in YEZHome_FE repository)
2. **Automatic deployment** to staging
3. **Access**: `https://test.yezhome.vn`

#### **Production Frontend**
1. **Manual workflow dispatch** in YEZHome_FE repository
2. **Type "DEPLOY"** to confirm
3. **Access**: `https://yezhome.vn`

## 🛠️ Manual Deployment

### **Using Deployment Script (Recommended)**

```bash
# Clone repository
git clone https://github.com/yourusername/YEZHome_Deploy.git
cd YEZHome_Deploy

# Create environment file
cp env.example .env
# Edit .env with your actual values

# Deploy everything
./deploy.sh production all

# Or deploy components separately
./deploy.sh production backend    # APIs only
./deploy.sh production frontend   # Website only
./deploy.sh staging all          # Staging environment
```

### **Using Docker Compose Directly**

#### **Backend Services**
```bash
# Production
cd YEZHome_Deploy
docker-compose -f docker-compose.yml up -d

# Staging
cd YEZHome_Deploy
docker-compose -f docker-compose.staging.yml up -d
```

#### **Frontend Services**
```bash
# Production
cd YEZHome_FE
docker-compose -f docker-compose.prod.yml up -d

# Staging
cd YEZHome_FE
docker-compose -f docker-compose.staging.yml up -d
```

## 🔒 SSL Certificate Management

### **Automatic SSL Setup**

SSL certificates are **automatically managed**:

1. **First Deployment**:
   - Nginx starts serving HTTP
   - Certbot requests SSL certificates from Let's Encrypt
   - Nginx configuration updated for HTTPS
   - HTTP traffic redirected to HTTPS

2. **Subsequent Deployments**:
   - **Existing certificates are reused**
   - **No new certificate generation**
   - **Zero downtime SSL**

3. **Automatic Renewal**:
   - Certificates renewed **30 days before expiration**
   - Cron job runs **twice daily**
   - **No manual intervention required**

### **SSL Certificate Lifecycle**

```
Day 1:  Deploy → Generate SSL cert → Valid for 90 days
Day 2:  Deploy → Use existing cert → No generation
Day 30: Deploy → Use existing cert → No generation  
Day 60: Auto-renewal → Extend cert → Valid for 90 more days
Day 61: Deploy → Use renewed cert → No generation
```

### **Manual SSL Operations**

```bash
# Check certificate status
openssl s_client -connect api.yezhome.vn:443 -servername api.yezhome.vn | openssl x509 -noout -dates

# Manual renewal (if needed)
docker exec yezhome-certbot-prod certbot renew
docker exec yezhome-nginx-prod nginx -s reload

# Check certificate files
docker exec yezhome-nginx-prod ls -la /etc/letsencrypt/live/
```

## 📊 Environment Configuration

### **Production Environment Variables**

```bash
# Deployment metadata
IMAGE_TAG=latest
BUILD_NUMBER=123
COMMIT_SHA=abc123def
LAST_DEPLOYED=2024-01-15 14:45:00 UTC

# SSL Configuration
CERTBOT_EMAIL=<EMAIL>

# Database Configuration
DB_HOST=yezhome-postgres
POSTGRES_DB=yezhome
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-secure-password

# JWT Configuration
JWT_KEY=your-production-jwt-key-32-chars-minimum
JWT_ISSUER=https://api.yezhome.vn
JWT_AUDIENCE=https://yezhome.vn
INTERNAL_JWT_KEY=your-production-internal-jwt-key
INTERNAL_JWT_ISSUER=https://adapi.yezhome.vn
INTERNAL_JWT_AUDIENCE=https://yezhome.vn

# AWS Configuration
AWS_S3_ACCESS_KEY=your-s3-key
AWS_S3_SECRET_KEY=your-s3-secret
AWS_S3_REGION=ap-southeast-1
AWS_S3_BUCKET_NAME=yezhome-prod-media
AWS_SES_ACCESS_KEY=your-ses-key
AWS_SES_SECRET_KEY=your-ses-secret
AWS_SES_REGION=ap-southeast-1
AWS_SES_FROM_EMAIL=<EMAIL>
AWS_SES_FROM_NAME=YEZHome

# Storage Configuration
STORAGE_PROVIDER=S3
```

### **Staging Environment Variables**

```bash
# Deployment metadata
IMAGE_TAG=latest
BUILD_NUMBER=123
COMMIT_SHA=abc123def
LAST_DEPLOYED=2024-01-15 10:30:00 UTC

# SSL Configuration
CERTBOT_EMAIL=<EMAIL>

# Database Configuration (same database, different environment)
DB_HOST=yezhome-postgres
POSTGRES_DB=yezhome
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-secure-password

# JWT Configuration (different keys for security)
JWT_KEY=your-staging-jwt-key-different-from-prod
JWT_ISSUER=https://api-test.yezhome.vn
JWT_AUDIENCE=https://test.yezhome.vn
INTERNAL_JWT_KEY=your-staging-internal-jwt-key
INTERNAL_JWT_ISSUER=https://adapi-test.yezhome.vn
INTERNAL_JWT_AUDIENCE=https://test.yezhome.vn

# AWS Configuration (staging resources)
AWS_S3_BUCKET_NAME=yezhome-staging-media
AWS_SES_FROM_EMAIL=<EMAIL>
AWS_SES_FROM_NAME=YEZHome Staging
# ... other AWS configs same as production
```

## 🔍 Monitoring & Troubleshooting

### **Check Deployment Status**

```bash
# Production services
cd /opt/yezhome/yezhome-prod
docker compose ps

# Staging services  
cd /opt/yezhome/yezhome-staging
docker compose -f docker-compose.staging.yml ps

# Check SSL certificates
curl -vI https://api.yezhome.vn
curl -vI https://api-test.yezhome.vn
```

### **View Logs**

```bash
# Production logs
cd /opt/yezhome/yezhome-prod
docker compose logs -f yezhome-api-prod
docker compose logs -f yezhome-nginx-prod

# Staging logs
cd /opt/yezhome/yezhome-staging
docker compose -f docker-compose.staging.yml logs -f yezhome-api-staging
docker compose -f docker-compose.staging.yml logs -f yezhome-nginx-staging

# SSL certificate logs
docker logs yezhome-certbot-prod
docker logs yezhome-certbot-staging
```

### **Common Issues & Solutions**

#### **SSL Certificate Generation Fails**
```bash
# Check if domains point to server
nslookup api.yezhome.vn

# Check if ports 80/443 are accessible
netstat -tlnp | grep :80
netstat -tlnp | grep :443

# Check certbot logs
docker logs yezhome-certbot-prod

# Manual certificate generation
docker exec yezhome-certbot-prod certbot certonly --webroot --webroot-path=/var/www/certbot --email <EMAIL> --agree-tos --no-eff-email -d api.yezhome.vn
```

#### **Nginx Configuration Errors**
```bash
# Test nginx configuration
docker exec yezhome-nginx-prod nginx -t

# Reload nginx configuration
docker exec yezhome-nginx-prod nginx -s reload

# Check nginx error logs
docker logs yezhome-nginx-prod
```

#### **Service Health Checks**
```bash
# Check API health
curl -f https://api.yezhome.vn/health
curl -f https://adapi.yezhome.vn/health

# Check frontend
curl -f https://yezhome.vn/health
```

## 🛡️ Security Features

### **SSL/TLS Security**
- ✅ **TLS 1.2+ only** - Secure protocols
- ✅ **Strong ciphers** - Modern encryption
- ✅ **HSTS headers** - Prevent downgrade attacks
- ✅ **Automatic renewal** - No expired certificates

### **Network Security**
- ✅ **Internal networking** - Services communicate internally
- ✅ **Rate limiting** - API protection
- ✅ **CORS configuration** - Cross-origin protection
- ✅ **Security headers** - XSS, clickjacking protection

### **Environment Isolation**
- ✅ **Separate JWT keys** - Different secrets per environment
- ✅ **Separate networks** - Docker network isolation
- ✅ **Separate volumes** - Data isolation
- ✅ **Separate domains** - Clear environment distinction

## 🎯 Best Practices

### **Development Workflow**
1. ✅ **Feature branches** → merge to `main` when ready
2. ✅ **Test in staging** before production deployment
3. ✅ **Small, frequent deployments** rather than big releases
4. ✅ **Always provide deployment reason** for audit trail
5. ✅ **Monitor after deployment** - Check logs and metrics

### **Deployment Strategy**
1. ✅ **Deploy to staging automatically** - Fast feedback
2. ✅ **Test thoroughly in staging** - Catch issues early
3. ✅ **Deploy to production manually** - Controlled releases
4. ✅ **Deploy during low-traffic hours** - Minimize impact
5. ✅ **Have rollback plan ready** - Quick recovery

### **SSL Management**
1. ✅ **Let certificates auto-renew** - Don't interfere
2. ✅ **Monitor certificate expiration** - Set up alerts
3. ✅ **Test SSL configuration** - Use SSL testing tools
4. ✅ **Backup certificates** - Store in secure location

## 🚨 Emergency Procedures

### **Rollback Deployment**
```bash
# Using GitHub Actions
# 1. Go to "Deploy to Droplet with SSL" workflow
# 2. Run with previous image tag
# 3. Specify reason: "Rollback due to issue"

# Manual rollback
cd /opt/yezhome/yezhome-prod
# Edit .env to change IMAGE_TAG to previous version
docker compose pull
docker compose up -d
```

### **SSL Certificate Issues**
```bash
# Force certificate regeneration
docker exec yezhome-certbot-prod rm -rf /etc/letsencrypt/live/api.yezhome.vn
docker restart yezhome-certbot-prod

# Use staging certificates for testing
# Set CERTBOT_STAGING=true in environment
```

### **Service Recovery**
```bash
# Restart all services
cd /opt/yezhome/yezhome-prod
docker compose restart

# Check service health
docker compose ps
docker compose logs --tail=50
```

## 📈 Scaling Considerations

For high-traffic scenarios:

1. **Load Balancing**:
   - Multiple backend instances behind Nginx
   - Frontend CDN integration
   - Database read replicas

2. **Performance**:
   - Redis for session management
   - Static asset optimization
   - Database query optimization

3. **Monitoring**:
   - Application performance monitoring
   - SSL certificate monitoring
   - Resource usage monitoring

## 🔗 Quick Reference

### **Access URLs**
- **Production Frontend**: https://yezhome.vn
- **Production API**: https://api.yezhome.vn
- **Production Internal API**: https://adapi.yezhome.vn
- **Staging Frontend**: https://test.yezhome.vn
- **Staging API**: https://api-test.yezhome.vn
- **Staging Internal API**: https://adapi-test.yezhome.vn

### **Key Commands**
```bash
# Deploy everything
./deploy.sh production all

# Check status
docker compose ps

# View logs
docker compose logs -f

# SSL status
curl -vI https://api.yezhome.vn

# Manual deployment
docker compose -f docker-compose.yml up -d
```

### **Important Files**
```
YEZHome_Deploy/
├── docker-compose.yml              # Production backend
├── docker-compose.staging.yml      # Staging backend
├── deploy.sh                       # Deployment script
├── nginx/                          # Nginx configurations
└── .github/workflows/              # GitHub Actions

YEZHome_FE/
├── Dockerfile.nginx                # Frontend with SSL
├── docker-compose.prod.yml         # Frontend production
├── docker-compose.staging.yml      # Frontend staging
└── nginx/                          # Frontend Nginx configs
```

---

This comprehensive guide covers all aspects of deploying YEZHome with automatic SSL certificates, Nginx reverse proxy, and multi-environment support. The setup provides production-ready security, automatic certificate management, and streamlined deployment workflows.

For questions or issues, refer to the troubleshooting section or check the service logs for detailed error information.
