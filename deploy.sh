#!/bin/bash

# YEZHome Deployment Script with SSL Support
# Usage: ./deploy.sh [production|staging] [backend|frontend|all]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if required environment variables are set
check_env_vars() {
    local required_vars=(
        "CERTBOT_EMAIL"
        "DB_HOST"
        "POSTGRES_DB" 
        "POSTGRES_USER"
        "POSTGRES_PASSWORD"
        "JWT_KEY"
        "JWT_ISSUER"
        "JWT_AUDIENCE"
        "INTERNAL_JWT_KEY"
        "INTERNAL_JWT_ISSUER"
        "INTERNAL_JWT_AUDIENCE"
    )
    
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        print_error "Missing required environment variables:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        echo ""
        echo "Please set these variables in your .env file or environment."
        exit 1
    fi
}

# Function to wait for service to be healthy
wait_for_service() {
    local service_name=$1
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for $service_name to be healthy..."
    
    while [[ $attempt -le $max_attempts ]]; do
        if docker ps --filter "name=$service_name" --filter "health=healthy" | grep -q "$service_name"; then
            print_success "$service_name is healthy!"
            return 0
        fi
        
        echo -n "."
        sleep 10
        ((attempt++))
    done
    
    print_warning "$service_name health check timed out"
    return 1
}

# Function to deploy backend services
deploy_backend() {
    local environment=$1
    
    print_status "Deploying backend services for $environment..."
    
    if [[ "$environment" == "production" ]]; then
        docker-compose -f docker-compose.yml down || true
        docker-compose -f docker-compose.yml pull
        docker-compose -f docker-compose.yml up -d
        
        # Wait for services to be ready
        wait_for_service "yezhome-nginx-prod"
        wait_for_service "yezhome-api-prod"
        wait_for_service "yezhome-internal-api-prod"
        
    elif [[ "$environment" == "staging" ]]; then
        docker-compose -f docker-compose.staging.yml down || true
        docker-compose -f docker-compose.staging.yml pull
        docker-compose -f docker-compose.staging.yml up -d
        
        # Wait for services to be ready
        wait_for_service "yezhome-nginx-staging"
        wait_for_service "yezhome-api-staging"
        wait_for_service "yezhome-internal-api-staging"
    fi
    
    print_success "Backend deployment completed for $environment"
}

# Function to deploy frontend
deploy_frontend() {
    local environment=$1
    
    print_status "Deploying frontend for $environment..."
    
    cd ../YEZHome_FE
    
    if [[ "$environment" == "production" ]]; then
        docker-compose -f docker-compose.prod.yml down || true
        docker-compose -f docker-compose.prod.yml build --no-cache
        docker-compose -f docker-compose.prod.yml up -d
        
        wait_for_service "yezhome-frontend-prod"
        
    elif [[ "$environment" == "staging" ]]; then
        docker-compose -f docker-compose.staging.yml down || true
        docker-compose -f docker-compose.staging.yml build --no-cache
        docker-compose -f docker-compose.staging.yml up -d
        
        wait_for_service "yezhome-frontend-staging"
    fi
    
    cd ../YEZHome_Deploy
    print_success "Frontend deployment completed for $environment"
}

# Function to check SSL certificates
check_ssl() {
    local environment=$1
    
    print_status "Checking SSL certificates for $environment..."
    
    if [[ "$environment" == "production" ]]; then
        local domains=("api.yezhome.vn" "adapi.yezhome.vn" "yezhome.vn")
    else
        local domains=("api-test.yezhome.vn" "adapi-test.yezhome.vn" "test.yezhome.vn")
    fi
    
    for domain in "${domains[@]}"; do
        print_status "Checking SSL for $domain..."
        if curl -sSf "https://$domain" >/dev/null 2>&1; then
            print_success "SSL certificate for $domain is working"
        else
            print_warning "SSL certificate for $domain may not be ready yet"
        fi
    done
}

# Function to show deployment status
show_status() {
    local environment=$1
    
    print_status "Deployment Status for $environment:"
    echo ""
    
    if [[ "$environment" == "production" ]]; then
        echo "Backend Services:"
        docker ps --filter "label=environment=production" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        echo ""
        echo "Frontend Service:"
        docker ps --filter "name=yezhome-frontend-prod" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        echo ""
        echo "Access URLs:"
        echo "  - Website: https://yezhome.vn"
        echo "  - API: https://api.yezhome.vn"
        echo "  - Internal API: https://adapi.yezhome.vn"
        
    else
        echo "Backend Services:"
        docker ps --filter "label=environment=staging" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        echo ""
        echo "Frontend Service:"
        docker ps --filter "name=yezhome-frontend-staging" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        echo ""
        echo "Access URLs:"
        echo "  - Website: https://test.yezhome.vn"
        echo "  - API: https://api-test.yezhome.vn"
        echo "  - Internal API: https://adapi-test.yezhome.vn"
    fi
}

# Main script logic
main() {
    local environment=${1:-""}
    local component=${2:-"all"}
    
    # Validate arguments
    if [[ "$environment" != "production" && "$environment" != "staging" ]]; then
        print_error "Usage: $0 [production|staging] [backend|frontend|all]"
        exit 1
    fi
    
    if [[ "$component" != "backend" && "$component" != "frontend" && "$component" != "all" ]]; then
        print_error "Component must be 'backend', 'frontend', or 'all'"
        exit 1
    fi
    
    print_status "Starting YEZHome deployment..."
    print_status "Environment: $environment"
    print_status "Component: $component"
    echo ""
    
    # Load environment variables
    if [[ -f ".env" ]]; then
        print_status "Loading environment variables from .env file..."
        set -a
        source .env
        set +a
    else
        print_warning "No .env file found. Make sure environment variables are set."
    fi
    
    # Check required environment variables
    check_env_vars
    
    # Deploy components
    case "$component" in
        "backend")
            deploy_backend "$environment"
            ;;
        "frontend")
            deploy_frontend "$environment"
            ;;
        "all")
            deploy_backend "$environment"
            sleep 10  # Wait a bit between deployments
            deploy_frontend "$environment"
            ;;
    esac
    
    # Wait a bit for services to fully start
    print_status "Waiting for services to fully initialize..."
    sleep 30
    
    # Check SSL certificates
    check_ssl "$environment"
    
    # Show deployment status
    echo ""
    show_status "$environment"
    
    print_success "Deployment completed successfully!"
    print_status "SSL certificates will be automatically generated and renewed."
    print_status "Monitor logs with: docker logs <container-name>"
}

# Run main function with all arguments
main "$@"
