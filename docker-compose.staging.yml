services:
  # === NGINX SERVICE (Đ<PERSON> được đơn giản hóa) ===
  nginx-staging:
    # build: . # Nếu Dockerfile của bạn chỉ để cài ssl-init.sh, có thể đổi thành image: nginx:latest
    image: nginx:latest
    container_name: yezhome-nginx-staging
    restart: always
    ports:
      - "8080:80"
      - "8443:443"
    volumes:
      - ./nginx/nginx-staging.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d-staging:/etc/nginx/conf.d:ro
      - certbot_certs_staging:/etc/letsencrypt
      - certbot_www_staging:/var/www/certbot
    networks:
      - yezhome-staging-network
    # SỬA ĐỔI QUAN TRỌNG:
    # Command này sẽ chờ cho đến khi certbot tạo ra chứng chỉ rồi mới khởi động Nginx
    command: >
      /bin/sh -c "
        while [ ! -f /etc/letsencrypt/live/api-test.yezhome.vn/fullchain.pem ]; do 
          echo 'Waiting for certificate...'
          sleep 5; 
        done && 
        nginx -g 'daemon off;'
      "
    labels:
      - "environment=staging"
      
  # === CERTBOT SERVICE (Service mới chuyên dụng) ===
  certbot:
    image: certbot/certbot
    container_name: yezhome-certbot-staging
    volumes:
      - certbot_certs_staging:/etc/letsencrypt
      - certbot_www_staging:/var/www/certbot
    # SỬA ĐỔI QUAN TRỌNG:
    # Command này sẽ chạy, gia hạn, và lặp lại việc kiểm tra sau mỗi 12 giờ
    command: >
      sh -c "
        while true; do
          certbot renew --quiet || certbot certonly --webroot --webroot-path=/var/www/certbot \
            --email ${CERTBOT_EMAIL} \
            --agree-tos \
            --no-eff-email \
            --staging \
            -d api-test.yezhome.vn \
            -d adapi-test.yezhome.vn;
          sleep 12h;
        done;
      "
      
  yezhome-api-staging:
    image: ghcr.io/gbnguyen1340/yezhome-api:${IMAGE_TAG:-latest}
    container_name: yezhome-api-staging
    restart: always
    expose:
      - "8080"  # Internal port only
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - ConnectionStrings__YezHomeConnection=Host=${DB_HOST};Database=${POSTGRES_DB};Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      - JWT__Key=${JWT_KEY}
      - JWT__Issuer=${JWT_ISSUER}
      - JWT__Audience=${JWT_AUDIENCE}
      - AWS__S3__AccessKey=${AWS_S3_ACCESS_KEY}
      - AWS__S3__SecretKey=${AWS_S3_SECRET_KEY}
      - AWS__S3__Region=${AWS_S3_REGION}
      - AWS__S3__BucketName=${AWS_S3_BUCKET_NAME}
      - AWS__SES__AccessKey=${AWS_SES_ACCESS_KEY}
      - AWS__SES__SecretKey=${AWS_SES_SECRET_KEY}
      - AWS__SES__Region=${AWS_SES_REGION}
      - AWS__SES__FromEmail=${AWS_SES_FROM_EMAIL}
      - AWS__SES__FromName=${AWS_SES_FROM_NAME}
      - Storage__Provider=${STORAGE_PROVIDER:-S3}
    volumes:
      - api_property_images:/app/PropertyImages
      - api_user_avatars:/app/UserAvatars
      - api_temp:/app/Temp
    networks:
      - yezhome-staging-network
    labels:
      - "environment=staging"
      - "commit_sha=${COMMIT_SHA:-unknown}"

  yezhome-internal-api-staging:
    image: ghcr.io/gbnguyen1340/yezhome-internal-api:${IMAGE_TAG:-latest}
    container_name: yezhome-internal-api-staging
    restart: always
    expose:
      - "8080"  # Internal port only
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - ConnectionStrings__YezHomeConnection=Host=${DB_HOST};Database=${POSTGRES_DB};Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      - JWT__Key=${INTERNAL_JWT_KEY}
      - JWT__Issuer=${INTERNAL_JWT_ISSUER}
      - JWT__Audience=${INTERNAL_JWT_AUDIENCE}
      - AWS__SES__AccessKey=${AWS_SES_ACCESS_KEY}
      - AWS__SES__SecretKey=${AWS_SES_SECRET_KEY}
      - AWS__SES__Region=${AWS_SES_REGION}
      - AWS__SES__FromEmail=${AWS_SES_FROM_EMAIL}
      - AWS__SES__FromName=${AWS_SES_FROM_NAME}
    networks:
      - yezhome-staging-network
    labels:
      - "environment=staging"
      - "commit_sha=${COMMIT_SHA:-unknown}"

volumes:
  api_property_images:
    name: yezhome_property_images
  api_user_avatars:
    name: yezhome_user_avatars
  api_temp:
    name: yezhome_temp
  certbot_certs_staging:
    name: yezhome_staging_certbot_certs
  certbot_www_staging:
    name: yezhome_staging_certbot_www

networks:
  yezhome-staging-network:
    name: yezhome-staging-network
    driver: bridge
